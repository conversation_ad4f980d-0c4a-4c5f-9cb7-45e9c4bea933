import * as React from 'react';

import * as SwitchPrimitive from '@radix-ui/react-switch';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '~/lib/utils';

const switchVariants = cva(
    'peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50',
    {
        variants: {
            size: {
                sm: 'h-3 w-5',
                default: 'h-[1.15rem] w-8',
                lg: 'h-6 w-10',
            },
        },
        defaultVariants: {
            size: 'default',
        },
    },
);

const switchThumbVariants = cva(
    'bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0',
    {
        variants: {
            size: {
                sm: 'size-2.5',
                default: 'size-4',
                lg: 'size-5',
            },
        },
        defaultVariants: {
            size: 'default',
        },
    },
);

function Switch({
    className,
    size,
    ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root> &
    VariantProps<typeof switchVariants>) {
    return (
        <SwitchPrimitive.Root
            data-slot='switch'
            className={cn(switchVariants({ size }), className)}
            {...props}
        >
            <SwitchPrimitive.Thumb
                data-slot='switch-thumb'
                className={cn(switchThumbVariants({ size }))}
            />
        </SwitchPrimitive.Root>
    );
}

export { Switch };
